//
//  Image.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import SwiftUI

extension View {
    /// 使视图在被点击时可裁剪（适用于包含图片的视图）
    func croppable(_ isCroppable: Binding<Bool>, image: Binding<UIImage?>) -> some View {
        modifier(CroppableModifier(isCroppable: isCroppable, image: image))
    }
}

struct CroppableModifier: ViewModifier {
    @Binding var isCroppable: Bool
    @Binding var image: UIImage?

    func body(content: Content) -> some View {
        content
            .onTapGesture {
                if image != nil {
                    isCroppable = true
                }
            }
            .sheet(isPresented: $isCroppable) {
                if let uiImage = image {
                    ImageEditor.cropper(image: uiImage) { edited in
                        if let edited = edited {
                            image = edited
                        }
                    }
                }
            }
    }
}

// MARK: - 圆角扩展
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}


/// 液态玻璃的样式
enum AnyGlassStyle {
    case regular
    case clear
    case identity
}

// MARK: 液态玻璃风格
extension View {
    @ViewBuilder
    func glassEffectIfAvailable(
        _ style: AnyGlassStyle = .regular
    ) -> some View {
        if #available(iOS 26.0, *) {
            switch style {
            case .regular:
                self.glassEffect(.regular)
            case .clear:
                self.glassEffect(.clear)
            case .identity:
                self.glassEffect(.identity)
            }
        } else {
            self
        }
    }
}
