//
//  CheckinDetailView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/17.
//

import SwiftUI
import MapKit

struct CheckinDetailView: View {
    let checkin: PlaceCheckin

    @State private var cameraPosition: MapCameraPosition
    @EnvironmentObject private var appSettings: AppSettings
    @StateObject private var logViewModel = LogViewModel()
    @StateObject private var checkinViewModel = PlaceCheckinViewModel()

    // 关联的出行记录
    @State private var relatedFootprint: UserFootprints?
    @State private var isLoadingFootprint = false

    // 删除确认对话框
    @State private var showDeleteAlert = false

    // 获取当前用户ID
    private let currentUserId = UserDefaults.standard.string(forKey: "currentUserId") ?? ""

    // 导航控制
    @Environment(\.dismiss) private var dismiss

    init(checkin: PlaceCheckin) {
        self.checkin = checkin

        // 初始化地图相机位置
        let region = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: checkin.latitude, longitude: checkin.longitude),
            span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
        )
        _cameraPosition = State(initialValue: .region(region))
    }

    var body: some View {
        ScrollView {
            VStack(spacing: Theme.Spacing.lg) {
                // 上半部分：地图
                mapView

                // 下半部分：详情信息
                detailInfoView

                // 关联的出行记录（如果有）
                if let footprint = relatedFootprint {
                    relatedTripView(footprint: footprint)
                }
            }
            .padding(Theme.Spacing.lg)
        }
        .navigationTitle("打卡详情")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            // 只有当前用户ID等于打卡记录的用户ID时才显示删除按钮
            if currentUserId == checkin.userId {
                ToolbarItem(placement: .topBarTrailing) {
                    Button(action: {
                        showDeleteAlert = true
                    }) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                    }
                }
            }
        }
        .alert("确认删除打卡记录?", isPresented: $showDeleteAlert) {
            Button("删除", role: .destructive) {
                Task {
                    await deleteCheckin()
                }
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("删除后将无法恢复")
        }
        .onAppear {
            loadRelatedFootprint()
        }
    }

    // MARK: - 地图视图
    private var mapView: some View {
        ZStack(alignment: .bottomTrailing) {
            Map(position: $cameraPosition, interactionModes: .all) {
                // 打卡点标注
                Annotation("打卡位置", coordinate: CLLocationCoordinate2D(latitude: checkin.latitude, longitude: checkin.longitude)) {
                    CheckinAnnotationView(checkin: checkin)
                }
            }
            .mapStyle(.standard(elevation: .flat,
                                emphasis: .muted,
                                pointsOfInterest: .including(.publicTransport),
                                showsTraffic: false))

            // 重新定位按钮
            Button(action: resetMapPosition) {
                Image(systemName: "location.viewfinder")
                    .font(.title2)
                    .padding(8)
                    .background(.white.opacity(0.3))
                    .clipShape(Circle())
                    .shadow(radius: 3)
            }
            .padding()
        }
        .frame(height: 300)
        .cornerRadius(Theme.CornerRadius.md)
    }

    // MARK: - 详情信息视图
    private var detailInfoView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            // 地点名称
            HStack {
                Image(systemName: "mappin.circle.fill")
                    .foregroundColor(.brandGreen)
                    .font(.title2)

                VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                    Text(checkin.displayName)
                        .font(.title3Brand)
                        .foregroundColor(.textPrimary)

                    Text("(\(String(format: "%.6f", checkin.latitude)), \(String(format: "%.6f", checkin.longitude)))")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }

                Spacer()
            }

            Divider()

            // 打卡时间
            HStack {
                Image(systemName: "clock.fill")
                    .foregroundColor(.textSecondary)
                Text("打卡时间:")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                Spacer()
                Text(checkin.formattedCreatedAt)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
            }

            // 相对时间
            HStack {
                Image(systemName: "timer")
                    .foregroundColor(.textSecondary)
                Text("距今:")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                Spacer()
                Text(checkin.relativeTimeString)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
            }

            // 描述（如果有）
            if let description = checkin.description, !description.isEmpty {
                Divider()

                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    HStack {
                        Image(systemName: "text.quote")
                            .foregroundColor(.textSecondary)
                        Text("打卡描述:")
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                        Spacer()
                    }

                    Text(description)
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)
                        .padding(Theme.Spacing.md)
                        .background(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                                .fill(Color.cardBackground.opacity(0.5))
                        )
                }
            }

            // 图片（如果有）
            if let photoURLs = checkin.photoURLs, !photoURLs.isEmpty {
                Divider()

                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    HStack {
                        Image(systemName: "photo.on.rectangle")
                            .foregroundColor(.textSecondary)
                        Text("打卡照片:")
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                        Spacer()
                        Text("\(photoURLs.count) 张")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                    }

                    // 图片网格
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: Theme.Spacing.sm), count: 2), spacing: Theme.Spacing.sm) {
                        ForEach(photoURLs, id: \.self) { photoURL in
                            AsyncImage(url: URL(string: photoURL)) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            } placeholder: {
                                Rectangle()
                                    .fill(Color.gray.opacity(0.3))
                                    .overlay(
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    )
                            }
                            .frame(height: 120)
                            .clipped()
                            .cornerRadius(Theme.CornerRadius.sm)
                        }
                    }
                }
            }
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.cardBackground)
        )
    }

    // MARK: - 关联出行记录视图
    private func relatedTripView(footprint: UserFootprints) -> some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            HStack {
                Image(systemName: "figure.walk")
                    .foregroundColor(.textSecondary)
                Text("关联出行:")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                Spacer()
            }

            // 使用 activityComponent 中的 tripActivityView
            tripActivityView(footprint: footprint)
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.cardBackground)
        )
    }

    // MARK: - 辅助方法

    /// 重置地图位置到打卡点
    private func resetMapPosition() {
        let region = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: checkin.latitude, longitude: checkin.longitude),
            span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
        )
        cameraPosition = .region(region)
    }

    /// 删除打卡记录
    private func deleteCheckin() async {
        await checkinViewModel.deleteCheckin(id: checkin.id)

        // 如果删除成功，返回上一页
        if checkinViewModel.errorMessage == nil {
            dismiss()
        }
    }

    /// 加载关联的出行记录
    private func loadRelatedFootprint() {
        guard let footprintId = checkin.userFootprintsId else { return }

        isLoadingFootprint = true

        Task {
            do {
                // 这里需要调用相应的API来获取出行记录详情
                // 暂时使用模拟数据，实际应该调用 FootprintManager 的相关方法
                // let footprint = try await footprintManager.getFootprintDetail(id: footprintId)
                // relatedFootprint = footprint

                print("需要加载出行记录ID: \(footprintId)")

            } catch {
                print("加载关联出行记录失败: \(error.localizedDescription)")
            }

            isLoadingFootprint = false
        }
    }
}

// MARK: - Preview

#Preview {
    NavigationView {
        CheckinDetailView(checkin: PlaceCheckin(
            id: "1",
            userId: "user1",
            position: "星巴克咖啡",
            latitude: 39.9042,
            longitude: 116.4074,
            photoURLs: [
                "https://example.com/photo1.jpg",
                "https://example.com/photo2.jpg"
            ],
            description: "在星巴克休息，喝了一杯拿铁，天气很好！",
            userFootprintsId: "footprint123",
            createdAt: Date(),
            updatedAt: Date()
        ))
    }
    .environmentObject(AppSettings())
}
